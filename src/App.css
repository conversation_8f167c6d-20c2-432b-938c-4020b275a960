/* App Component Styles */
.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--modern-dark-bg);
  color: var(--modern-text-primary);
}

main {
  flex: 1;
}

/* Hero Section */
.hero-section {
  background: var(--gradient-primary);
  color: white;
  padding: 100px 0;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Product Cards */
.product-card {
  border: none;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.product-image {
  height: 250px;
  object-fit: cover;
  width: 100%;
}

/* Feature Cards */
.feature-card {
  text-align: center;
  padding: 2rem;
  border-radius: 15px;
  background: white;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.feature-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

/* Testimonial Cards */
.testimonial-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.testimonial-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 1rem;
}

/* Pricing Cards */
.pricing-card {
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.pricing-card.featured {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.pricing-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
}

/* Section Spacing */
.section-padding {
  padding: 80px 0;
}

/* Custom Buttons */
.btn-custom {
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.btn-outline-custom {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-custom:hover {
  background: var(--primary-color);
  color: white;
}

/* Interactive Elements */
.transition-all {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* Hero Section Enhancements */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
}

/* Product Card Hover Effects */
.product-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.product-card img {
  transition: transform 0.3s ease;
}

.product-card:hover img {
  transform: scale(1.05);
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Gradient Text Animation */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.gradient-text-animated {
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Scroll Indicator */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-primary);
  transform-origin: 0%;
  z-index: 9999;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-secondary);
}

/* Modern CSS Variables - TapOnn Inspired */
:root {
  /* Original Variables */
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

  /* EcnoDev Color Palette */
  --ecnodev-primary: #4ade80;
  --ecnodev-primary-dark: #22c55e;
  --ecnodev-bg-primary: #0f172a;
  --ecnodev-bg-secondary: #1e293b;
  --ecnodev-bg-accent: #334155;
  --ecnodev-text-primary: #ffffff;
  --ecnodev-text-secondary: #cbd5e1;
  --ecnodev-text-muted: #64748b;
  --ecnodev-gradient: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  --ecnodev-glass: rgba(255, 255, 255, 0.1);
  --ecnodev-glass-border: rgba(255, 255, 255, 0.2);
  --ecnodev-code-bg: #1e293b;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--ecnodev-text-primary);
  background-color: var(--ecnodev-bg-primary);
  overflow-x: hidden;
}

/* Utility Classes */
.gradient-bg {
  background: var(--ecnodev-gradient);
}

.gradient-text {
  background: var(--ecnodev-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-custom {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.btn-gradient {
  background: var(--ecnodev-gradient);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(74, 222, 128, 0.3);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern Navigation Styles */
.modern-navbar {
  background: rgba(15, 23, 42, 0.95) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--ecnodev-glass-border);
  transition: all 0.3s ease;
}

.modern-navbar.scrolled {
  background: rgba(15, 23, 42, 0.98) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modern-logo {
  color: var(--ecnodev-text-primary) !important;
  text-decoration: none !important;
  font-weight: 800;
  font-size: 1.5rem;
}

.logo-icon {
  color: var(--ecnodev-primary);
  animation: pulse 2s infinite;
}

.logo-text {
  color: var(--ecnodev-text-primary);
}

.logo-accent {
  background: var(--ecnodev-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-subtitle {
  display: block;
  font-size: 0.6rem;
  font-weight: 400;
  color: var(--ecnodev-text-muted);
  letter-spacing: 1px;
  margin-top: -2px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.modern-nav-link {
  color: var(--ecnodev-text-secondary) !important;
  font-weight: 500;
  padding: 0.75rem 1.5rem !important;
  position: relative;
  transition: all 0.3s ease;
  text-decoration: none !important;
}

.modern-nav-link:hover {
  color: var(--ecnodev-primary) !important;
}

.modern-nav-link.active {
  color: var(--ecnodev-primary) !important;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: var(--ecnodev-gradient);
  border-radius: 2px;
}

.nav-actions {
  gap: 1rem;
}

.action-btn {
  background: var(--ecnodev-glass);
  border: 1px solid var(--ecnodev-glass-border);
  color: var(--ecnodev-text-secondary);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-btn:hover {
  background: rgba(74, 222, 128, 0.2);
  border-color: var(--ecnodev-primary);
  color: var(--ecnodev-primary);
}

.cart-badge {
  font-size: 0.7rem;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-toggler-icon {
  background-image: none !important;
}

.navbar-collapse {
  transition: all 0.3s ease-in-out;
}

.navbar-collapse.collapsing {
  transition: height 0.3s ease;
}

.cart-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--modern-accent-cyan);
  color: var(--modern-dark-bg);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-dropdown {
  background: var(--modern-dark-secondary) !important;
  border: 1px solid var(--modern-glass-border) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px);
}

.modern-dropdown .dropdown-item {
  color: var(--modern-text-secondary) !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.3s ease;
}

.modern-dropdown .dropdown-item:hover {
  background: rgba(74, 222, 128, 0.1) !important;
  color: var(--ecnodev-primary) !important;
}

.modern-dropdown {
  background: var(--ecnodev-glass) !important;
  border: 1px solid var(--ecnodev-glass-border) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(20px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;
  padding: 0.5rem !important;
}

.modern-dropdown .dropdown-item {
  color: var(--ecnodev-text-secondary) !important;
  border-radius: 8px !important;
  margin-bottom: 0.25rem !important;
  transition: all 0.3s ease;
}

/* EcnoDev Hero Section */
.modern-hero {
  background: var(--ecnodev-bg-dark);
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(74, 222, 128, 0.1) 0%, transparent 70%);
  z-index: 0;
}

.hero-content {
  z-index: 2;
  position: relative;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  color: var(--ecnodev-text-primary);
  margin-bottom: 1.5rem;
}

.hero-highlight {
  background: var(--ecnodev-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--ecnodev-text-secondary);
  margin-bottom: 1rem;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--ecnodev-text-muted);
  line-height: 1.6;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary-modern {
  background: var(--ecnodev-gradient);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(74, 222, 128, 0.3);
}

.btn-primary-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(74, 222, 128, 0.4);
}

.btn-outline-modern {
  background: transparent;
  border: 2px solid var(--ecnodev-glass-border);
  color: var(--ecnodev-text-primary);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.btn-outline-modern:hover {
  background: var(--ecnodev-glass);
  border-color: var(--ecnodev-primary);
  color: var(--ecnodev-primary);
  transform: translateY(-2px);
}

/* Hero Visual */
.hero-visual {
  position: relative;
  z-index: 2;
}

.hero-image-container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.hero-image-container img {
  transition: transform 0.3s ease;
}

.hero-image-container:hover img {
  transform: scale(1.02);
}

/* Why Us Section - Minimal Design */
.why-us-section {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%);
  position: relative;
  padding: 6rem 0;
  color: #1a1a1a;
}

/* Key Benefits Section - Minimal Design */
.key-benefits-section {
  background: var(--ecnodev-bg-primary);
  position: relative;
  padding: 6rem 0;
  color: var(--ecnodev-text-primary);
}

.why-us-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at 30% 20%, rgba(74, 222, 128, 0.03) 0%, transparent 70%),
              radial-gradient(ellipse at 70% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

.why-us-section .why-us-badge {
  background: #4ade80;
  border: none;
  color: #ffffff;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.5rem 1.25rem;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 2px;
  display: inline-block;
}

.why-us-section .why-us-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.why-us-section .why-us-subtitle {
  font-size: 1.25rem;
  color: #4ade80;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 1.5rem;
}

.why-us-section .why-us-description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.7;
  margin-bottom: 2rem;
  max-width: 500px;
}

.why-us-highlight {
  color: #1a1a1a;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.6;
  padding: 2rem;
  background: #f8fafc;
  border-left: 3px solid #4ade80;
  border-radius: 8px;
  margin-top: 2rem;
}

.why-us-features {
  position: relative;
}

.why-us-feature-item {
  position: relative;
  padding: 2rem 0;
  margin-bottom: 2.5rem;
  transition: all 0.3s ease;
}

.why-us-feature-item:hover {
  transform: translateY(-2px);
}

.feature-number-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
  margin-right: 2rem;
}

.feature-number {
  width: 48px;
  height: 48px;
  background: #4ade80;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
}

.feature-icon {
  color: #4ade80;
  font-size: 1.25rem;
}

.feature-content {
  flex: 1;
}

.why-us-section .feature-title {
  color: #1a1a1a;
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}

.why-us-section .feature-description {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
}

/* Key Benefits Section Styling */
.key-benefits-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at 30% 20%, rgba(74, 222, 128, 0.05) 0%, transparent 70%),
              radial-gradient(ellipse at 70% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.key-benefits-section .benefits-badge {
  background: var(--ecnodev-primary);
  border: none;
  color: #ffffff;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.5rem 1.25rem;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 2px;
  display: inline-block;
}

.key-benefits-section .benefits-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--ecnodev-text-primary);
  line-height: 1.2;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.key-benefits-section .benefits-subtitle {
  font-size: 1.125rem;
  color: var(--ecnodev-text-muted);
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Simple Card Design for Benefits */
.benefit-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.benefit-card:hover {
  transform: translateY(-5px);
  border-color: rgba(74, 222, 128, 0.3);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.benefit-icon-simple {
  width: 60px;
  height: 60px;
  background: rgba(74, 222, 128, 0.1);
  border: 1px solid rgba(74, 222, 128, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon-simple {
  background: rgba(74, 222, 128, 0.2);
  border-color: var(--ecnodev-primary);
  transform: scale(1.1);
}

.benefit-title-simple {
  color: var(--ecnodev-text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: -0.01em;
}

.benefit-description-simple {
  color: var(--ecnodev-text-muted);
  font-size: 0.95rem;
  line-height: 1.6;
}

/* Brand Scroller Section */
.brand-scroller-section {
  padding: 1.5rem 0;
  overflow: hidden;
}

.brand-scroller-container {
  width: 100%;
  overflow: hidden;
}

.brand-scroller {
  display: flex;
  animation: scroll 25s linear infinite;
  white-space: nowrap;
}

.brand-item {
  font-size: 1.5rem;
  font-weight: 600;
  color: #6b7280;
  margin-right: 3rem;
  flex-shrink: 0;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Phone Mockup */

.phone-mockup {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.phone-frame {
  width: 280px;
  height: 560px;
  background: linear-gradient(145deg, var(--ecnodev-bg-accent), var(--ecnodev-bg-secondary));
  border-radius: 40px;
  padding: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  position: relative;
  margin: 0 auto;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: linear-gradient(145deg, var(--ecnodev-bg-secondary), var(--ecnodev-bg-dark));
  border-radius: 30px;
  padding: 20px;
  overflow: hidden;
}

.profile-card {
  text-align: center;
  color: white;
}

.profile-header {
  margin-bottom: 30px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--ecnodev-gradient);
  margin: 0 auto 15px;
}

.profile-card h4 {
  color: white;
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.profile-card p {
  color: var(--ecnodev-text-secondary);
  font-size: 0.9rem;
}

.social-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.social-icon {
  width: 50px;
  height: 50px;
  border-radius: 15px;
  position: relative;
}

.social-icon.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-icon.whatsapp {
  background: #25d366;
}

.social-icon.linkedin {
  background: #0077b5;
}

.social-icon.twitter {
  background: #1da1f2;
}

.social-icon.youtube {
  background: #ff0000;
}

.social-icon.tiktok {
  background: linear-gradient(45deg, #ff0050, #00f2ea);
}

/* NFC Card */
.nfc-card {
  position: absolute;
  top: 50%;
  right: -50px;
  transform: translateY(-50%);
  width: 120px;
  height: 80px;
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.card-content {
  text-align: center;
  color: #333;
}

.card-logo {
  font-size: 1.5rem;
  color: var(--ecnodev-primary);
  margin-bottom: 5px;
}

.card-text {
  font-weight: 800;
  font-size: 1.2rem;
  letter-spacing: 2px;
}

/* Hero Stats */
.hero-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 60px;
  padding: 0 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--ecnodev-text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--ecnodev-text-secondary);
  margin-top: 5px;
  line-height: 1.2;
}

/* Hero Bottom Text */
.hero-bottom-text {
  position: absolute;
  bottom: 40px;
  left: 0;
  right: 0;
  z-index: 2;
}

.hero-bottom-text p {
  color: var(--ecnodev-text-muted);
  font-size: 0.9rem;
  letter-spacing: 2px;
  font-weight: 500;
}

/* Responsive Design */

/* Large screens and up */
@media (min-width: 1200px) {
  .modern-navbar .container {
    max-width: 1200px;
  }

  .logo-text {
    font-size: 1.1rem;
  }
}

/* Medium screens */
@media (max-width: 991px) {
  .modern-navbar {
    padding: 0.75rem 0;
  }

  .logo-text {
    font-size: 0.95rem;
  }

  .logo-subtitle {
    font-size: 0.7rem;
  }

  .modern-nav-link {
    padding: 0.5rem 1rem !important;
  }

  .nav-actions {
    gap: 0.75rem;
  }

  .action-btn {
    width: 36px;
    height: 36px;
  }
}

/* Small screens and mobile */
@media (max-width: 768px) {
  .modern-navbar {
    padding: 0.5rem 0;
  }

  .modern-navbar .navbar-brand {
    margin-right: 0;
  }

  .logo-text {
    font-size: 0.9rem;
  }

  .logo-subtitle {
    display: none;
  }

  .navbar-toggler {
    border: none !important;
    padding: 0.25rem 0.5rem;
    background: var(--ecnodev-glass) !important;
    border-radius: 8px;
  }

  .navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(74, 222, 128, 0.25) !important;
  }

  .navbar-collapse {
    margin-top: 1rem;
    background: var(--ecnodev-glass);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid var(--ecnodev-glass-border);
    backdrop-filter: blur(20px);
  }

  .modern-nav-link {
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    margin-bottom: 0.25rem;
    display: block;
    text-align: center;
  }

  .modern-nav-link:hover {
    background: rgba(74, 222, 128, 0.1);
  }

  .nav-actions {
    justify-content: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--ecnodev-glass-border);
    gap: 1rem;
  }

  .action-btn {
    width: 44px;
    height: 44px;
  }

  /* Dropdown styles for mobile */
  .dropdown-menu {
    background: var(--ecnodev-glass) !important;
    border: 1px solid var(--ecnodev-glass-border) !important;
    border-radius: 12px !important;
    backdrop-filter: blur(20px);
    margin-top: 0.5rem !important;
  }

  .dropdown-item {
    color: var(--ecnodev-text-secondary) !important;
    padding: 0.75rem 1rem !important;
    border-radius: 8px !important;
    margin: 0.25rem !important;
  }

  .dropdown-item:hover {
    background: rgba(74, 222, 128, 0.1) !important;
    color: var(--ecnodev-primary) !important;
  }

  /* Hero section mobile styles */
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .btn-primary-modern,
  .btn-outline-modern {
    width: 100%;
    margin-bottom: 1rem;
  }

  .phone-frame {
    width: 220px;
    height: 440px;
  }

  .nfc-card {
    right: -30px;
    width: 100px;
    height: 65px;
  }

  .hero-stats {
    flex-direction: column;
    gap: 20px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .hero-image-container {
    max-width: 100%;
    padding: 0 1rem;
  }

  /* Why Us Section Mobile */
  .why-us-section {
    padding: 4rem 0;
  }

  .why-us-title {
    font-size: 2.5rem;
  }

  .why-us-subtitle {
    font-size: 1.1rem;
  }

  .why-us-description {
    font-size: 1rem;
  }

  .why-us-feature-item {
    padding: 1.5rem 0;
    margin-bottom: 2rem;
  }

  .feature-number-container {
    min-width: 60px;
    margin-right: 1.5rem;
  }

  .feature-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .feature-title {
    font-size: 1.1rem;
  }

  .feature-description {
    font-size: 0.95rem;
  }

  /* Brand Scroller Mobile */
  .brand-scroller-section {
    padding: 1rem 0;
  }

  .brand-item {
    font-size: 1.25rem;
    margin-right: 2.5rem;
  }
}

/* Extra small screens */
@media (max-width: 576px) {
  .modern-navbar .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .logo-text {
    font-size: 0.85rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .navbar-collapse {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  /* Why Us Section Extra Small */
  .why-us-section {
    padding: 3rem 0;
  }

  .why-us-title {
    font-size: 2rem;
  }

  .why-us-subtitle {
    font-size: 1rem;
  }

  .why-us-feature-item {
    padding: 1rem 0;
    margin-bottom: 1.5rem;
  }

  .feature-number-container {
    margin-right: 1rem;
    min-width: 50px;
  }

  .feature-number {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  /* Brand Scroller Extra Small */
  .brand-scroller-section {
    padding: 0.75rem 0;
  }

  .brand-item {
    font-size: 1rem;
    margin-right: 2rem;
  }

  .feature-title {
    font-size: 1rem;
  }

  .feature-description {
    font-size: 0.9rem;
  }
}

/* Footer Styles */
footer.bg-dark {
  background-color: var(--ecnodev-bg-secondary) !important;
  border-top: 1px solid var(--ecnodev-glass-border);
}

footer .text-light {
  color: var(--ecnodev-text-primary) !important;
}

footer .text-muted {
  color: var(--ecnodev-text-muted) !important;
}

footer a.text-light:hover {
  color: var(--ecnodev-primary) !important;
  transition: color 0.3s ease;
}

footer a.text-muted:hover {
  color: var(--ecnodev-primary) !important;
  transition: color 0.3s ease;
}

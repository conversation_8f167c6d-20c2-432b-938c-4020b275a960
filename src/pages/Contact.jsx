import { useState } from 'react'
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap'
import { FiMail, FiPhone, FiMapPin, FiClock, FiSend } from 'react-icons/fi'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })
  const [showAlert, setShowAlert] = useState(false)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Handle form submission
    console.log('Contact form submitted:', formData)
    setShowAlert(true)
    setFormData({ name: '', email: '', subject: '', message: '' })
    setTimeout(() => setShowAlert(false), 5000)
  }

  const contactInfo = [
    {
      icon: <FiMail className="text-primary" size={24} />,
      title: "Email Us",
      content: "<EMAIL>",
      description: "Send us an email anytime"
    },
    {
      icon: <FiPhone className="text-primary" size={24} />,
      title: "Call Us", 
      content: "+****************",
      description: "Mon-Fri from 8am to 5pm"
    },
    {
      icon: <FiMapPin className="text-primary" size={24} />,
      title: "Visit Us",
      content: "123 Business Street",
      description: "City, State 12345, USA"
    },
    {
      icon: <FiClock className="text-primary" size={24} />,
      title: "Business Hours",
      content: "Mon - Fri: 8am - 5pm",
      description: "Weekend: 10am - 2pm"
    }
  ]

  return (
    <div className="fade-in">
      {/* Hero Section */}
      <section className="gradient-bg text-white py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <h1 className="display-4 fw-bold mb-3">Contact Us</h1>
              <p className="lead">
                Have questions about our NFC cards? We're here to help!
                Reach out to our friendly EcnoDev team for support and information.
              </p>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Contact Information */}
      <section className="section-padding">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <h2 className="display-5 fw-bold mb-3">Get in Touch</h2>
              <p className="lead text-muted">
                We'd love to hear from you. Choose the best way to reach us.
              </p>
            </Col>
          </Row>
          <Row>
            {contactInfo.map((info, index) => (
              <Col lg={3} md={6} key={index} className="mb-4">
                <Card className="text-center border-0 shadow-sm h-100">
                  <Card.Body>
                    <div className="mb-3">
                      {info.icon}
                    </div>
                    <h5 className="fw-bold mb-2">{info.title}</h5>
                    <p className="fw-bold text-primary mb-1">{info.content}</p>
                    <small className="text-muted">{info.description}</small>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Contact Form */}
      <section className="bg-light section-padding">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto">
              <Card className="border-0 shadow-sm">
                <Card.Body className="p-5">
                  <div className="text-center mb-4">
                    <h3 className="fw-bold mb-2">Send us a Message</h3>
                    <p className="text-muted">
                      Fill out the form below and we'll get back to you within 24 hours.
                    </p>
                  </div>

                  {showAlert && (
                    <Alert variant="success" className="d-flex align-items-center">
                      <FiSend className="me-2" />
                      Thank you for your message! We'll get back to you soon.
                    </Alert>
                  )}

                  <Form onSubmit={handleSubmit}>
                    <Row>
                      <Col md={6} className="mb-3">
                        <Form.Label>Full Name *</Form.Label>
                        <Form.Control
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                          placeholder="Enter your full name"
                        />
                      </Col>
                      <Col md={6} className="mb-3">
                        <Form.Label>Email Address *</Form.Label>
                        <Form.Control
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          placeholder="Enter your email"
                        />
                      </Col>
                    </Row>
                    
                    <div className="mb-3">
                      <Form.Label>Subject *</Form.Label>
                      <Form.Select
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">Select a subject</option>
                        <option value="general">General Inquiry</option>
                        <option value="product">Product Information</option>
                        <option value="support">Technical Support</option>
                        <option value="bulk">Bulk Orders</option>
                        <option value="partnership">Partnership</option>
                        <option value="other">Other</option>
                      </Form.Select>
                    </div>
                    
                    <div className="mb-4">
                      <Form.Label>Message *</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={5}
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        placeholder="Tell us how we can help you..."
                      />
                    </div>
                    
                    <div className="d-grid">
                      <Button 
                        type="submit" 
                        size="lg" 
                        className="btn-gradient"
                      >
                        <FiSend className="me-2" />
                        Send Message
                      </Button>
                    </div>
                  </Form>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* FAQ Section */}
      <section className="section-padding">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <h2 className="display-5 fw-bold mb-4">Frequently Asked Questions</h2>
              <p className="text-muted mb-5">
                Quick answers to common questions. Can't find what you're looking for? 
                Contact us directly!
              </p>
              
              <Row className="text-start">
                <Col md={6} className="mb-4">
                  <h5 className="fw-bold">How do NFC cards work?</h5>
                  <p className="text-muted">
                    Simply tap your NFC card on any NFC-enabled smartphone to instantly 
                    share your contact information, no app required.
                  </p>
                </Col>
                <Col md={6} className="mb-4">
                  <h5 className="fw-bold">What's the delivery time?</h5>
                  <p className="text-muted">
                    Standard delivery takes 3-5 business days. Express shipping 
                    options are available for faster delivery.
                  </p>
                </Col>
                <Col md={6} className="mb-4">
                  <h5 className="fw-bold">Can I customize my card design?</h5>
                  <p className="text-muted">
                    Yes! We offer full customization options including colors, 
                    logos, and text to match your brand.
                  </p>
                </Col>
                <Col md={6} className="mb-4">
                  <h5 className="fw-bold">Do you offer bulk discounts?</h5>
                  <p className="text-muted">
                    Absolutely! Contact us for special pricing on orders of 
                    50+ cards for teams and organizations.
                  </p>
                </Col>
              </Row>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Map Section */}
      <section className="bg-light py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <h3 className="fw-bold mb-3">Find Our Office</h3>
              <p className="text-muted mb-4">
                Visit our headquarters for in-person consultations and support.
              </p>
              <div className="bg-secondary rounded-3 p-5">
                <p className="text-white mb-0">
                  Interactive Map Placeholder<br />
                  123 Business Street, City, State 12345
                </p>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  )
}

export default Contact

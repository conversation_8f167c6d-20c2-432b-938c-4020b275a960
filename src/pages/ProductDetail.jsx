import { useState } from 'react'
import { useParams } from 'react-router-dom'
import { Container, Row, Col, Button, Card, Badge, Tab, Tabs } from 'react-bootstrap'
import { FiStar, FiHeart, FiShoppingCart, FiCheck } from 'react-icons/fi'

const ProductDetail = () => {
  const { id } = useParams()
  const [quantity, setQuantity] = useState(1)
  const [selectedColor, setSelectedColor] = useState('black')

  // Mock product data - in real app, fetch based on id
  const product = {
    id: 1,
    name: "Classic Black NFC Card",
    price: 29.99,
    originalPrice: 39.99,
    rating: 4.8,
    reviews: 124,
    description: "Our Classic Black NFC Card combines elegance with cutting-edge technology. Made from premium materials with a matte black finish, this card makes a lasting impression while providing instant contact sharing capabilities.",
    features: [
      "NFC Technology for instant sharing",
      "QR Code backup for non-NFC devices", 
      "Waterproof and durable design",
      "Lifetime profile updates",
      "Compatible with iOS and Android",
      "Professional matte finish"
    ],
    specifications: {
      "Dimensions": "85.6 × 53.98 × 0.76 mm",
      "Material": "Premium PVC with NFC chip",
      "Weight": "5.5 grams",
      "Compatibility": "All NFC-enabled devices",
      "Range": "Up to 4cm",
      "Durability": "Waterproof, scratch-resistant"
    },
    colors: [
      { name: 'black', label: 'Matte Black', hex: '#000000' },
      { name: 'white', label: 'Pearl White', hex: '#ffffff' },
      { name: 'blue', label: 'Navy Blue', hex: '#1e3a8a' }
    ]
  }

  const reviews = [
    {
      name: "John Smith",
      rating: 5,
      date: "2024-01-15",
      comment: "Excellent quality and works perfectly. Very professional looking!"
    },
    {
      name: "Sarah Johnson", 
      rating: 5,
      date: "2024-01-10",
      comment: "Love how easy it is to share my contact info. Great investment!"
    },
    {
      name: "Mike Chen",
      rating: 4,
      date: "2024-01-05", 
      comment: "Good product, fast shipping. The NFC works great on all phones I've tested."
    }
  ]

  return (
    <div className="fade-in">
      <Container className="py-5">
        <Row>
          {/* Product Images */}
          <Col lg={6} className="mb-4">
            <Card className="border-0 shadow-sm">
              <div className="bg-gradient-primary" style={{ height: '400px', borderRadius: '8px' }}>
                {/* Product image placeholder */}
              </div>
            </Card>
            <Row className="mt-3">
              <Col xs={3}>
                <div className="bg-light border rounded" style={{ height: '80px' }}></div>
              </Col>
              <Col xs={3}>
                <div className="bg-light border rounded" style={{ height: '80px' }}></div>
              </Col>
              <Col xs={3}>
                <div className="bg-light border rounded" style={{ height: '80px' }}></div>
              </Col>
              <Col xs={3}>
                <div className="bg-light border rounded" style={{ height: '80px' }}></div>
              </Col>
            </Row>
          </Col>

          {/* Product Info */}
          <Col lg={6}>
            <div className="mb-3">
              <Badge bg="success" className="mb-2">Best Seller</Badge>
              <h1 className="fw-bold">{product.name}</h1>
              <div className="d-flex align-items-center mb-3">
                <div className="d-flex align-items-center me-3">
                  {[...Array(5)].map((_, i) => (
                    <FiStar 
                      key={i} 
                      className={`${i < Math.floor(product.rating) ? 'text-warning' : 'text-muted'}`}
                      fill={i < Math.floor(product.rating) ? 'currentColor' : 'none'}
                    />
                  ))}
                  <span className="fw-bold ms-2">{product.rating}</span>
                </div>
                <span className="text-muted">({product.reviews} reviews)</span>
              </div>
            </div>

            <div className="mb-4">
              <div className="d-flex align-items-center mb-2">
                <span className="h3 text-primary fw-bold mb-0">${product.price}</span>
                <span className="text-muted text-decoration-line-through ms-2 h5">
                  ${product.originalPrice}
                </span>
                <Badge bg="danger" className="ms-2">
                  {Math.round((1 - product.price / product.originalPrice) * 100)}% OFF
                </Badge>
              </div>
              <p className="text-success fw-bold">✅ In Stock - Free Shipping</p>
            </div>

            <p className="text-muted mb-4">{product.description}</p>

            {/* Color Selection */}
            <div className="mb-4">
              <h6 className="fw-bold mb-3">Color:</h6>
              <div className="d-flex gap-2">
                {product.colors.map((color) => (
                  <Button
                    key={color.name}
                    variant={selectedColor === color.name ? "primary" : "outline-secondary"}
                    size="sm"
                    onClick={() => setSelectedColor(color.name)}
                    className="d-flex align-items-center gap-2"
                  >
                    <div 
                      className="rounded-circle border"
                      style={{ 
                        width: '20px', 
                        height: '20px', 
                        backgroundColor: color.hex,
                        border: color.hex === '#ffffff' ? '1px solid #dee2e6' : 'none'
                      }}
                    ></div>
                    {color.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div className="mb-4">
              <h6 className="fw-bold mb-3">Quantity:</h6>
              <div className="d-flex align-items-center gap-3">
                <Button 
                  variant="outline-secondary" 
                  size="sm"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                >
                  -
                </Button>
                <span className="fw-bold">{quantity}</span>
                <Button 
                  variant="outline-secondary" 
                  size="sm"
                  onClick={() => setQuantity(quantity + 1)}
                >
                  +
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="d-grid gap-2 mb-4">
              <Button size="lg" className="btn-gradient">
                <FiShoppingCart className="me-2" />
                Add to Cart - ${(product.price * quantity).toFixed(2)}
              </Button>
              <Button variant="outline-primary" size="lg">
                <FiHeart className="me-2" />
                Add to Wishlist
              </Button>
            </div>

            {/* Features */}
            <div>
              <h6 className="fw-bold mb-3">Key Features:</h6>
              <ul className="list-unstyled">
                {product.features.map((feature, index) => (
                  <li key={index} className="d-flex align-items-center mb-2">
                    <FiCheck className="text-success me-2" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </Col>
        </Row>

        {/* Product Details Tabs */}
        <Row className="mt-5">
          <Col>
            <Tabs defaultActiveKey="description" className="mb-4">
              <Tab eventKey="description" title="Description">
                <div className="p-4">
                  <h5 className="fw-bold mb-3">Product Description</h5>
                  <p>{product.description}</p>
                  <p>
                    Our NFC business cards represent the perfect fusion of traditional networking 
                    and modern technology. Each card is carefully crafted with premium materials 
                    and embedded with advanced NFC technology that allows instant sharing of your 
                    contact information with just a simple tap.
                  </p>
                </div>
              </Tab>
              
              <Tab eventKey="specifications" title="Specifications">
                <div className="p-4">
                  <h5 className="fw-bold mb-3">Technical Specifications</h5>
                  <Row>
                    {Object.entries(product.specifications).map(([key, value]) => (
                      <Col md={6} key={key} className="mb-3">
                        <strong>{key}:</strong> {value}
                      </Col>
                    ))}
                  </Row>
                </div>
              </Tab>
              
              <Tab eventKey="reviews" title={`Reviews (${product.reviews})`}>
                <div className="p-4">
                  <h5 className="fw-bold mb-4">Customer Reviews</h5>
                  {reviews.map((review, index) => (
                    <Card key={index} className="mb-3 border-0 bg-light">
                      <Card.Body>
                        <div className="d-flex justify-content-between align-items-start mb-2">
                          <div>
                            <h6 className="fw-bold mb-1">{review.name}</h6>
                            <div className="d-flex align-items-center">
                              {[...Array(5)].map((_, i) => (
                                <FiStar 
                                  key={i} 
                                  className={`${i < review.rating ? 'text-warning' : 'text-muted'}`}
                                  fill={i < review.rating ? 'currentColor' : 'none'}
                                  size={14}
                                />
                              ))}
                            </div>
                          </div>
                          <small className="text-muted">{review.date}</small>
                        </div>
                        <p className="mb-0">{review.comment}</p>
                      </Card.Body>
                    </Card>
                  ))}
                </div>
              </Tab>
            </Tabs>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default ProductDetail

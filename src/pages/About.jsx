import { Container, <PERSON>, <PERSON>, <PERSON>, Badge } from 'react-bootstrap'
import { FiTarget, FiEye, FiHeart, FiUsers, FiAward, FiTrendingUp } from 'react-icons/fi'

const About = () => {
  const stats = [
    { icon: <FiUsers />, number: "10,000+", label: "Happy Customers" },
    { icon: <FiAward />, number: "50+", label: "Countries Served" },
    { icon: <FiTrendingUp />, number: "99.9%", label: "Customer Satisfaction" },
    { icon: <FiHeart />, number: "24/7", label: "Customer Support" }
  ]

  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      bio: "Visionary leader with 15+ years in tech innovation"
    },
    {
      name: "<PERSON>", 
      role: "CTO",
      bio: "Expert in NFC technology and product development"
    },
    {
      name: "<PERSON>",
      role: "Head of Design",
      bio: "Creative director focused on user experience"
    }
  ]

  return (
    <div className="fade-in">
      {/* Hero Section */}
      <section className="gradient-bg text-white py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <h1 className="display-4 fw-bold mb-3">About NFCCards by EcnoDev</h1>
              <p className="lead">
                We're revolutionizing professional networking with smart NFC technology,
                making connections more meaningful and sustainable. Powered by EcnoDev's
                innovative solutions and cutting-edge technology.
              </p>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Our Story */}
      <section className="section-padding">
        <Container>
          <Row className="align-items-center">
            <Col lg={6} className="mb-4 mb-lg-0">
              <h2 className="display-5 fw-bold mb-4">Our Story</h2>
              <p className="text-muted mb-4">
                NFCCards is a flagship product of EcnoDev, launched to revolutionize professional
                networking. Built on EcnoDev's foundation of innovative technology solutions,
                NFCCards addresses the outdated, wasteful nature of traditional business cards.
              </p>
              <p className="text-muted mb-4">
                Leveraging EcnoDev's expertise in digital solutions, we serve over 10,000
                professionals worldwide, helping them make lasting connections while reducing
                paper waste. Our NFC cards have facilitated millions of seamless contact
                exchanges across 50+ countries.
              </p>
              <p className="text-muted">
                As part of the EcnoDev ecosystem, we believe networking should be effortless,
                memorable, and sustainable. Visit <a href="https://ecnodev.com" className="text-primary text-decoration-none" target="_blank" rel="noopener noreferrer">ecnodev.com</a> to
                explore our full range of innovative technology solutions.
              </p>
            </Col>
            <Col lg={6}>
              <div className="bg-gradient-primary rounded-4 p-5 text-white text-center">
                <h3 className="fw-bold mb-3">Our Impact</h3>
                <p className="mb-0">
                  Over 1 million paper business cards saved from waste through our 
                  sustainable NFC technology solutions.
                </p>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Mission, Vision, Values */}
      <section className="bg-light section-padding">
        <Container>
          <Row>
            <Col lg={4} className="mb-4">
              <Card className="feature-card h-100 border-0">
                <Card.Body className="text-center">
                  <FiTarget className="feature-icon text-primary" />
                  <h4 className="fw-bold mb-3">Our Mission</h4>
                  <p className="text-muted">
                    To revolutionize professional networking by providing innovative, 
                    sustainable, and user-friendly NFC solutions that create meaningful connections.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={4} className="mb-4">
              <Card className="feature-card h-100 border-0">
                <Card.Body className="text-center">
                  <FiEye className="feature-icon text-primary" />
                  <h4 className="fw-bold mb-3">Our Vision</h4>
                  <p className="text-muted">
                    A world where every professional interaction is seamless, memorable, 
                    and environmentally responsible through smart technology.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={4} className="mb-4">
              <Card className="feature-card h-100 border-0">
                <Card.Body className="text-center">
                  <FiHeart className="feature-icon text-primary" />
                  <h4 className="fw-bold mb-3">Our Values</h4>
                  <p className="text-muted">
                    Innovation, sustainability, customer-centricity, and quality drive 
                    everything we do. We're committed to excellence in every interaction.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Stats */}
      <section className="section-padding">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <h2 className="display-5 fw-bold mb-3">Our Achievements</h2>
              <p className="lead text-muted">
                Numbers that reflect our commitment to excellence and customer satisfaction
              </p>
            </Col>
          </Row>
          <Row>
            {stats.map((stat, index) => (
              <Col lg={3} md={6} key={index} className="mb-4">
                <Card className="text-center border-0 bg-light h-100">
                  <Card.Body>
                    <div className="text-primary mb-3" style={{fontSize: '3rem'}}>
                      {stat.icon}
                    </div>
                    <h3 className="fw-bold text-primary mb-2">{stat.number}</h3>
                    <p className="text-muted mb-0">{stat.label}</p>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Team */}
      <section className="bg-light section-padding">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <h2 className="display-5 fw-bold mb-3">Meet Our Team</h2>
              <p className="lead text-muted">
                The passionate individuals behind NFCCards innovation
              </p>
            </Col>
          </Row>
          <Row>
            {team.map((member, index) => (
              <Col lg={4} md={6} key={index} className="mb-4">
                <Card className="text-center border-0 shadow-sm h-100">
                  <Card.Body>
                    <div 
                      className="bg-gradient-primary rounded-circle mx-auto mb-3"
                      style={{width: '100px', height: '100px'}}
                    ></div>
                    <h5 className="fw-bold mb-1">{member.name}</h5>
                    <p className="text-primary fw-bold mb-2">{member.role}</p>
                    <p className="text-muted small">{member.bio}</p>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Key Benefits */}
      <section className="key-benefits-section">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <Badge className="benefits-badge mb-3">Key Benefits</Badge>
              <h2 className="benefits-title mb-4">What makes us different</h2>
              <p className="benefits-subtitle">
                Discover the unique advantages that set NFCCards apart from traditional networking solutions
              </p>
            </Col>
          </Row>
          <Row className="justify-content-center">
            <Col lg={6} md={6} className="mb-4">
              <Card className="benefit-card h-100 border-0">
                <Card.Body className="text-center p-4">
                  <div className="benefit-icon-simple mb-3">🌱</div>
                  <h5 className="benefit-title-simple mb-3">Eco-Friendly</h5>
                  <p className="benefit-description-simple text-muted mb-0">
                    Reduce paper waste and environmental impact with our sustainable NFC solutions.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={6} md={6} className="mb-4">
              <Card className="benefit-card h-100 border-0">
                <Card.Body className="text-center p-4">
                  <div className="benefit-icon-simple mb-3">🚀</div>
                  <h5 className="benefit-title-simple mb-3">Cutting-Edge Technology</h5>
                  <p className="benefit-description-simple text-muted mb-0">
                    Latest NFC technology ensuring compatibility and reliability across all devices.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={6} md={6} className="mb-4">
              <Card className="benefit-card h-100 border-0">
                <Card.Body className="text-center p-4">
                  <div className="benefit-icon-simple mb-3">🎨</div>
                  <h5 className="benefit-title-simple mb-3">Custom Design</h5>
                  <p className="benefit-description-simple text-muted mb-0">
                    Personalized designs that reflect your brand and professional identity.
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={6} md={6} className="mb-4">
              <Card className="benefit-card h-100 border-0">
                <Card.Body className="text-center p-4">
                  <div className="benefit-icon-simple mb-3">💎</div>
                  <h5 className="benefit-title-simple mb-3">Premium Quality</h5>
                  <p className="benefit-description-simple text-muted mb-0">
                    High-quality materials and craftsmanship in every card we produce.
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>

      {/* EcnoDev Section */}
      <section className="gradient-bg text-white section-padding">
        <Container>
          <Row className="text-center">
            <Col lg={8} className="mx-auto">
              <h2 className="display-5 fw-bold mb-4">Powered by EcnoDev</h2>
              <p className="lead mb-4">
                NFCCards is proudly developed and maintained by EcnoDev, a leading
                technology company specializing in innovative digital solutions.
              </p>
              <p className="mb-4">
                EcnoDev brings years of expertise in software development, digital
                transformation, and cutting-edge technology to deliver products that
                make a real difference in how businesses and professionals connect.
              </p>
              <a
                href="https://ecnodev.com"
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-light btn-lg px-5"
              >
                Visit EcnoDev.com
              </a>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  )
}

export default About

import { useState, useEffect } from 'react'
import { Con<PERSON>er, <PERSON>, Col, <PERSON>, <PERSON>, Badge } from 'react-bootstrap'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import CountUp from 'react-countup'
import VideoModal from '../components/VideoModal'
import { FiZap, FiSmartphone, FiUsers, FiShield, FiStar, FiArrowRight, FiPlay, FiCheck, FiTrendingUp, FiWifi } from 'react-icons/fi'

const Home = () => {
  const [showVideoModal, setShowVideoModal] = useState(false)
  const [statsRef, statsInView] = useInView({ threshold: 0.3, triggerOnce: true })
  const [featuresRef, featuresInView] = useInView({ threshold: 0.2, triggerOnce: true })

  const stats = [
    { number: 50000, suffix: "+", label: "Happy Customers" },
    { number: 95, suffix: "%", label: "Customer Satisfaction" },
    { number: 24, suffix: "/7", label: "Support Available" },
    { number: 100, suffix: "+", label: "Countries Served" }
  ]

  const features = [
    {
      icon: <FiZap className="feature-icon" />,
      title: "Instant Sharing",
      description: "Share your contact information with just a tap. No apps required for recipients.",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=200&fit=crop"
    },
    {
      icon: <FiSmartphone className="feature-icon" />,
      title: "NFC Technology",
      description: "Works with all NFC-enabled smartphones. Compatible with iOS and Android devices.",
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=200&fit=crop"
    },
    {
      icon: <FiUsers className="feature-icon" />,
      title: "Professional Networking",
      description: "Make lasting impressions at conferences, meetings, and networking events.",
      image: "https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=300&h=200&fit=crop"
    },
    {
      icon: <FiShield className="feature-icon" />,
      title: "Secure & Private",
      description: "Your data is encrypted and secure. Control what information you share.",
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=300&h=200&fit=crop"
    }
  ]

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Marketing Director",
      company: "TechCorp",
      rating: 5,
      text: "These NFC cards have revolutionized how I network. No more fumbling with business cards!",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
    },
    {
      name: "Michael Chen",
      role: "Entrepreneur",
      company: "StartupXYZ",
      rating: 5,
      text: "Professional, convenient, and eco-friendly. My clients love the modern approach.",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
    },
    {
      name: "Emily Davis",
      role: "Sales Manager",
      company: "SalesForce Inc",
      rating: 5,
      text: "The customization options are amazing. Perfect for our brand identity.",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
    }
  ]

  const products = [
    {
      id: 1,
      name: "Classic Black",
      price: 29.99,
      originalPrice: 39.99,
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",
      badge: "Best Seller",
      features: ["NFC Enabled", "QR Code", "Waterproof"]
    },
    {
      id: 2,
      name: "Premium White",
      price: 34.99,
      originalPrice: 44.99,
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
      badge: "Premium",
      features: ["NFC Enabled", "Custom Design", "Premium Finish"]
    },
    {
      id: 3,
      name: "Metal Edition",
      price: 49.99,
      originalPrice: 59.99,
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",
      badge: "Luxury",
      features: ["Metal Body", "Laser Engraving", "Premium Feel"]
    }
  ]

  return (
    <div className="modern-home">
      {/* Hero Section */}
      <section className="modern-hero position-relative overflow-hidden">
        <div className="hero-background"></div>
        <Container>
          <Row className="align-items-center min-vh-100 py-5">
            <Col lg={6} className="hero-content">
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <h1 className="hero-title mb-4">
                  #1 Platform for
                  <span className="hero-highlight d-block">Information Sharing</span>
                </h1>
                <h2 className="hero-subtitle mb-4">
                  Share Instantly. Connect Effortlessly
                </h2>
                <p className="hero-description mb-5">
                  Your ultimate digital profile platform for seamless information
                  sharing, connecting smartly while embracing sustainable, eco-
                  friendly practices. Powered by EcnoDev's innovative technology.
                </p>

                <div className="hero-actions mb-5">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button className="btn-primary-modern me-3 mb-3">
                      Get NFCCards
                    </Button>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button variant="outline-light" className="btn-outline-modern mb-3">
                      Create my Profile
                    </Button>
                  </motion.div>
                </div>
              </motion.div>
            </Col>

            <Col lg={6} className="text-center position-relative">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="hero-visual"
              >
                {/* Hero Image */}
                <div className="hero-image-container position-relative">
                  <img
                    src="/ChatGPT Image Aug 24, 2025, 09_19_47 PM.png"
                    alt="NFC Cards Digital Profile Platform"
                    className="img-fluid rounded-4 shadow-lg"
                    style={{
                      maxWidth: '100%',
                      height: 'auto',
                      filter: 'drop-shadow(0 20px 40px rgba(0,0,0,0.2))'
                    }}
                  />

                  {/* Floating Animation Elements */}
                  <motion.div
                    className="position-absolute"
                    style={{ top: '10%', right: '10%' }}
                    animate={{
                      y: [0, -10, 0],
                      rotate: [0, 5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <div className="bg-success rounded-circle p-3 shadow-lg">
                      <FiWifi className="text-white" size={24} />
                    </div>
                  </motion.div>
                </div>

                {/* Stats */}
                <div className="hero-stats">
                  <div className="stat-item">
                    <div className="stat-number">1M+</div>
                    <div className="stat-label">Connections<br />Made</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number">100k+</div>
                    <div className="stat-label">Active Users</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number">100+</div>
                    <div className="stat-label">Global Brands</div>
                  </div>
                </div>
              </motion.div>
            </Col>
          </Row>
        </Container>

        {/* Bottom Text */}
        <div className="hero-bottom-text">
          <Container>
            <div className="text-center">
              <p className="mb-0">POWERED BY ECNODEV - TRUSTED BY PROFESSIONALS WORLDWIDE</p>
            </div>
          </Container>
        </div>
      </section>

      {/* Why Us Section */}
      <section className="why-us-section py-5">
        <Container>
          <Row className="align-items-center">
            <Col lg={6} className="mb-5 mb-lg-0">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="why-us-content">
                  <Badge className="why-us-badge mb-3">Why us?</Badge>
                  <h2 className="why-us-title mb-4">
                    Designed to be remembered.
                  </h2>
                  <p className="why-us-subtitle mb-4">
                    NFCCards helps you leave a mark that lasts.
                  </p>
                  <p className="why-us-description mb-5">
                    It's not a card that gets crumpled, tossed, or forgotten. You're
                    too memorable for a flimsy rectangle.
                  </p>
                  <p className="why-us-highlight">
                    <strong>Tap and make an impression that lives in their phone, not their drawer.</strong>
                  </p>
                </div>
              </motion.div>
            </Col>

            <Col lg={6}>
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <div className="why-us-features">
                  {[
                    {
                      number: "1",
                      icon: <FiSmartphone />,
                      title: "One card, infinite shares.",
                      description: "No more running out of paper cards. Just tap and you share your details, socials and links that live online too. YOU."
                    },
                    {
                      number: "2",
                      icon: <FiZap />,
                      title: "Stand out instantly.",
                      description: "Say goodbye to forgettable interactions. OneTap cards a first look to every interaction and keeps you top of their mind."
                    },
                    {
                      number: "3",
                      icon: <FiUsers />,
                      title: "Your profile, your way.",
                      description: "Make your online presence as memorable as you are. Our fully custom AI alerts."
                    }
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      className="why-us-feature-item d-flex mb-4"
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.2 }}
                      viewport={{ once: true }}
                    >
                      <div className="feature-number-container me-4">
                        <div className="feature-number">
                          {item.number}
                        </div>
                        <div className="feature-icon">
                          {item.icon}
                        </div>
                      </div>
                      <div className="feature-content">
                        <h5 className="feature-title mb-2">{item.title}</h5>
                        <p className="feature-description text-muted mb-0">
                          {item.description}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Brand Scroller Section */}
      <section className="brand-scroller-section">
        <div className="brand-scroller-container">
          <div className="brand-scroller">
            {[...Array(20)].map((_, index) => (
              <div key={index} className="brand-item">
                WeConnect
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-5 bg-light">
        <Container>
          <div ref={statsRef}>
            <Row>
              {stats.map((stat, index) => (
                <Col lg={3} md={6} key={index} className="mb-4 text-center">
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={statsInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <h2 className="display-4 fw-bold text-primary mb-0">
                      {statsInView && (
                        <CountUp
                          end={stat.number}
                          duration={2}
                          suffix={stat.suffix}
                        />
                      )}
                    </h2>
                    <p className="text-muted fw-bold">{stat.label}</p>
                  </motion.div>
                </Col>
              ))}
            </Row>
          </div>
        </Container>
      </section>

      {/* Key Benefits Section */}
      <section className="key-benefits-section">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={featuresInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6 }}
              >
                <Badge className="benefits-badge mb-3">Key Benefits</Badge>
                <h2 className="benefits-title mb-4">
                  Everything you need to network smarter
                </h2>
                <p className="benefits-subtitle">
                  Discover the advantages that make our NFC cards the perfect choice for modern professionals
                </p>
              </motion.div>
            </Col>
          </Row>
          <div ref={featuresRef}>
            <Row className="justify-content-center">
              {features.map((feature, index) => (
                <Col lg={6} md={6} key={index} className="mb-4">
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={featuresInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card className="benefit-card h-100 border-0">
                      <Card.Body className="text-center p-4">
                        <div className="benefit-icon-simple mb-3">
                          {feature.icon}
                        </div>
                        <h5 className="benefit-title-simple mb-3">{feature.title}</h5>
                        <p className="benefit-description-simple text-muted mb-0">
                          {feature.description}
                        </p>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>
              ))}
            </Row>
          </div>
        </Container>
      </section>

      {/* Product Showcase */}
      <section className="section-padding bg-light">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="display-5 fw-bold mb-3">Featured Products</h2>
                <p className="lead text-muted">
                  Choose from our collection of premium NFC business cards
                </p>
              </motion.div>
            </Col>
          </Row>
          <Row>
            {products.map((product, index) => (
              <Col lg={4} md={6} key={product.id} className="mb-4">
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10 }}
                >
                  <Card className="product-card h-100 border-0 shadow-sm">
                    <div className="position-relative overflow-hidden">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="card-img-top"
                        style={{ height: '250px', objectFit: 'cover' }}
                      />
                      <Badge
                        bg={product.badge === 'Best Seller' ? 'success' : product.badge === 'Premium' ? 'primary' : 'warning'}
                        className="position-absolute top-0 start-0 m-3"
                      >
                        {product.badge}
                      </Badge>
                      <div className="position-absolute top-0 end-0 m-3">
                        <div className="bg-white rounded-circle p-2 shadow-sm">
                          <FiTrendingUp className="text-success" size={16} />
                        </div>
                      </div>
                    </div>
                    <Card.Body>
                      <h5 className="fw-bold mb-2">{product.name}</h5>
                      <div className="mb-3">
                        {product.features.map((feature, idx) => (
                          <Badge key={idx} bg="light" text="dark" className="me-1 mb-1 small">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <div>
                          <span className="h5 mb-0 text-primary fw-bold">${product.price}</span>
                          <span className="text-muted text-decoration-line-through ms-2 small">
                            ${product.originalPrice}
                          </span>
                        </div>
                        <Badge bg="danger">
                          {Math.round((1 - product.price / product.originalPrice) * 100)}% OFF
                        </Badge>
                      </div>
                      <div className="d-grid gap-2">
                        <Button
                          as={Link}
                          to={`/product/${product.id}`}
                          variant="primary"
                          className="btn-gradient"
                        >
                          View Details
                        </Button>
                        <Button variant="outline-primary" size="sm">
                          Add to Cart
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
          <Row>
            <Col className="text-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Button as={Link} to="/products" variant="primary" size="lg" className="btn-gradient px-5 py-3">
                  View All Products <FiArrowRight className="ms-2" />
                </Button>
              </motion.div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Testimonials */}
      <section className="section-padding">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="display-5 fw-bold mb-3">What Our Customers Say</h2>
                <p className="lead text-muted">
                  Join thousands of satisfied professionals who've upgraded their networking
                </p>
              </motion.div>
            </Col>
          </Row>
          <Row>
            {testimonials.map((testimonial, index) => (
              <Col lg={4} md={6} key={index} className="mb-4">
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                >
                  <Card className="testimonial-card h-100 border-0 shadow-sm">
                    <Card.Body className="text-center">
                      <div className="d-flex justify-content-center mb-3">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <motion.div
                            key={i}
                            initial={{ opacity: 0, scale: 0 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.5 + i * 0.1 }}
                            viewport={{ once: true }}
                          >
                            <FiStar className="text-warning me-1" fill="currentColor" />
                          </motion.div>
                        ))}
                      </div>
                      <p className="text-muted mb-4 fst-italic">"{testimonial.text}"</p>
                      <div className="d-flex align-items-center justify-content-center">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="rounded-circle me-3"
                          style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                        />
                        <div className="text-start">
                          <h6 className="fw-bold mb-0">{testimonial.name}</h6>
                          <small className="text-muted">{testimonial.role}</small>
                          <div className="small text-primary">{testimonial.company}</div>
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* How It Works */}
      <section className="section-padding bg-light">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="display-5 fw-bold mb-3">How It Works</h2>
                <p className="lead text-muted">
                  Get started with your NFC business card in just 3 simple steps
                </p>
              </motion.div>
            </Col>
          </Row>
          <Row>
            {[
              {
                step: "1",
                title: "Choose Your Design",
                description: "Select from our premium collection or create a custom design that matches your brand",
                icon: "🎨",
                image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=300&h=200&fit=crop"
              },
              {
                step: "2",
                title: "Set Up Your Profile",
                description: "Add your contact information, social media links, and customize what you want to share",
                icon: "📱",
                image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=200&fit=crop"
              },
              {
                step: "3",
                title: "Start Networking",
                description: "Simply tap your card on any smartphone to instantly share your information",
                icon: "🤝",
                image: "https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=300&h=200&fit=crop"
              }
            ].map((step, index) => (
              <Col lg={4} md={6} key={index} className="mb-4">
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="position-relative mb-4">
                    <img
                      src={step.image}
                      alt={step.title}
                      className="img-fluid rounded-4 shadow-sm"
                      style={{ height: '200px', width: '100%', objectFit: 'cover' }}
                    />
                    <div className="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 rounded-4 d-flex align-items-center justify-content-center">
                      <div className="bg-white rounded-circle p-3 shadow-lg">
                        <span style={{ fontSize: '2rem' }}>{step.icon}</span>
                      </div>
                    </div>
                    <Badge
                      bg="primary"
                      className="position-absolute top-0 start-0 m-3 rounded-circle"
                      style={{ width: '40px', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                    >
                      {step.step}
                    </Badge>
                  </div>
                  <h4 className="fw-bold mb-3">{step.title}</h4>
                  <p className="text-muted">{step.description}</p>
                </motion.div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="gradient-bg text-white py-5 position-relative overflow-hidden">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="display-5 fw-bold mb-3">Ready to Upgrade Your Networking?</h2>
                <p className="lead mb-4">
                  Join the smart networking revolution. Get your NFC business card today and never lose a connection again.
                </p>

                <div className="d-flex justify-content-center gap-3 mb-4 flex-wrap">
                  <Button
                    as={Link}
                    to="/products"
                    variant="light"
                    size="lg"
                    className="btn-custom px-5 py-3"
                  >
                    Get Started Now <FiArrowRight className="ms-2" />
                  </Button>
                  <Button
                    variant="outline-light"
                    size="lg"
                    className="btn-custom px-5 py-3"
                    onClick={() => setShowVideoModal(true)}
                  >
                    <FiPlay className="me-2" />
                    Watch Demo
                  </Button>
                </div>

                <div className="d-flex justify-content-center align-items-center gap-4 text-white-50 flex-wrap">
                  <div className="d-flex align-items-center">
                    <FiCheck className="me-2 text-success" />
                    <span>Free Worldwide Shipping</span>
                  </div>
                  <div className="d-flex align-items-center">
                    <FiCheck className="me-2 text-success" />
                    <span>30-Day Money Back</span>
                  </div>
                  <div className="d-flex align-items-center">
                    <FiCheck className="me-2 text-success" />
                    <span>Lifetime Support</span>
                  </div>
                </div>
              </motion.div>
            </Col>
          </Row>
        </Container>

        {/* Floating elements */}
        <motion.div
          className="position-absolute top-0 start-0 text-white-50"
          style={{ fontSize: '6rem' }}
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          ⚡
        </motion.div>
        <motion.div
          className="position-absolute bottom-0 end-0 text-white-50"
          style={{ fontSize: '4rem' }}
          animate={{ y: [0, -20, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          📱
        </motion.div>
      </section>

      {/* Video Modal */}
      <VideoModal
        show={showVideoModal}
        onHide={() => setShowVideoModal(false)}
      />
    </div>
  )
}

export default Home

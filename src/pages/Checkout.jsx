import { useState } from 'react'
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap'
import { FiLock, FiCreditCard, FiTruck } from 'react-icons/fi'

const Checkout = () => {
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    nameOnCard: ''
  })

  const orderSummary = {
    items: [
      { name: "Classic Black NFC Card", quantity: 2, price: 29.99 },
      { name: "Premium White NFC Card", quantity: 1, price: 34.99 }
    ],
    subtotal: 94.97,
    shipping: 0,
    tax: 7.60,
    total: 102.57
  }

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Handle form submission
    console.log('Order submitted:', formData)
  }

  return (
    <div className="fade-in">
      <Container className="py-5">
        <Row>
          <Col>
            <h1 className="fw-bold mb-4">Checkout</h1>
            
            {/* Progress Steps */}
            <div className="d-flex justify-content-center mb-5">
              <div className="d-flex align-items-center">
                <div className={`rounded-circle d-flex align-items-center justify-content-center ${step >= 1 ? 'bg-primary text-white' : 'bg-light'}`} style={{width: '40px', height: '40px'}}>
                  1
                </div>
                <div className={`mx-3 ${step >= 2 ? 'text-primary' : 'text-muted'}`} style={{width: '50px', height: '2px', backgroundColor: step >= 2 ? 'var(--primary-color)' : '#dee2e6'}}></div>
                <div className={`rounded-circle d-flex align-items-center justify-content-center ${step >= 2 ? 'bg-primary text-white' : 'bg-light'}`} style={{width: '40px', height: '40px'}}>
                  2
                </div>
                <div className={`mx-3 ${step >= 3 ? 'text-primary' : 'text-muted'}`} style={{width: '50px', height: '2px', backgroundColor: step >= 3 ? 'var(--primary-color)' : '#dee2e6'}}></div>
                <div className={`rounded-circle d-flex align-items-center justify-content-center ${step >= 3 ? 'bg-primary text-white' : 'bg-light'}`} style={{width: '40px', height: '40px'}}>
                  3
                </div>
              </div>
            </div>
          </Col>
        </Row>

        <Row>
          <Col lg={8}>
            <Form onSubmit={handleSubmit}>
              {/* Step 1: Contact Information */}
              {step === 1 && (
                <Card className="border-0 shadow-sm mb-4">
                  <Card.Header className="bg-light border-0">
                    <h5 className="fw-bold mb-0">
                      <FiTruck className="me-2" />
                      Contact & Shipping Information
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Row>
                      <Col md={12} className="mb-3">
                        <Form.Label>Email Address</Form.Label>
                        <Form.Control
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={6} className="mb-3">
                        <Form.Label>First Name</Form.Label>
                        <Form.Control
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={6} className="mb-3">
                        <Form.Label>Last Name</Form.Label>
                        <Form.Control
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={12} className="mb-3">
                        <Form.Label>Address</Form.Label>
                        <Form.Control
                          type="text"
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={6} className="mb-3">
                        <Form.Label>City</Form.Label>
                        <Form.Control
                          type="text"
                          name="city"
                          value={formData.city}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={3} className="mb-3">
                        <Form.Label>State</Form.Label>
                        <Form.Select
                          name="state"
                          value={formData.state}
                          onChange={handleInputChange}
                          required
                        >
                          <option value="">Select State</option>
                          <option value="CA">California</option>
                          <option value="NY">New York</option>
                          <option value="TX">Texas</option>
                          {/* Add more states */}
                        </Form.Select>
                      </Col>
                      <Col md={3} className="mb-3">
                        <Form.Label>ZIP Code</Form.Label>
                        <Form.Control
                          type="text"
                          name="zipCode"
                          value={formData.zipCode}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                    </Row>
                    <div className="d-flex justify-content-end">
                      <Button 
                        variant="primary" 
                        className="btn-gradient"
                        onClick={() => setStep(2)}
                      >
                        Continue to Payment
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              )}

              {/* Step 2: Payment Information */}
              {step === 2 && (
                <Card className="border-0 shadow-sm mb-4">
                  <Card.Header className="bg-light border-0">
                    <h5 className="fw-bold mb-0">
                      <FiCreditCard className="me-2" />
                      Payment Information
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Alert variant="info" className="d-flex align-items-center">
                      <FiLock className="me-2" />
                      Your payment information is secure and encrypted
                    </Alert>
                    
                    <Row>
                      <Col md={12} className="mb-3">
                        <Form.Label>Card Number</Form.Label>
                        <Form.Control
                          type="text"
                          name="cardNumber"
                          placeholder="1234 5678 9012 3456"
                          value={formData.cardNumber}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={12} className="mb-3">
                        <Form.Label>Name on Card</Form.Label>
                        <Form.Control
                          type="text"
                          name="nameOnCard"
                          value={formData.nameOnCard}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={6} className="mb-3">
                        <Form.Label>Expiry Date</Form.Label>
                        <Form.Control
                          type="text"
                          name="expiryDate"
                          placeholder="MM/YY"
                          value={formData.expiryDate}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                      <Col md={6} className="mb-3">
                        <Form.Label>CVV</Form.Label>
                        <Form.Control
                          type="text"
                          name="cvv"
                          placeholder="123"
                          value={formData.cvv}
                          onChange={handleInputChange}
                          required
                        />
                      </Col>
                    </Row>
                    
                    <div className="d-flex justify-content-between">
                      <Button 
                        variant="outline-secondary"
                        onClick={() => setStep(1)}
                      >
                        Back to Shipping
                      </Button>
                      <Button 
                        variant="primary" 
                        className="btn-gradient"
                        onClick={() => setStep(3)}
                      >
                        Review Order
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              )}

              {/* Step 3: Order Review */}
              {step === 3 && (
                <Card className="border-0 shadow-sm mb-4">
                  <Card.Header className="bg-light border-0">
                    <h5 className="fw-bold mb-0">Review Your Order</h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="mb-4">
                      <h6 className="fw-bold">Shipping Address:</h6>
                      <p className="text-muted mb-0">
                        {formData.firstName} {formData.lastName}<br />
                        {formData.address}<br />
                        {formData.city}, {formData.state} {formData.zipCode}
                      </p>
                    </div>
                    
                    <div className="mb-4">
                      <h6 className="fw-bold">Payment Method:</h6>
                      <p className="text-muted mb-0">
                        **** **** **** {formData.cardNumber.slice(-4)}
                      </p>
                    </div>
                    
                    <div className="d-flex justify-content-between">
                      <Button 
                        variant="outline-secondary"
                        onClick={() => setStep(2)}
                      >
                        Back to Payment
                      </Button>
                      <Button 
                        type="submit"
                        variant="success" 
                        size="lg"
                        className="btn-gradient"
                      >
                        Place Order - ${orderSummary.total}
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              )}
            </Form>
          </Col>

          {/* Order Summary Sidebar */}
          <Col lg={4}>
            <Card className="border-0 shadow-sm position-sticky" style={{top: '20px'}}>
              <Card.Header className="bg-light border-0">
                <h5 className="fw-bold mb-0">Order Summary</h5>
              </Card.Header>
              <Card.Body>
                {orderSummary.items.map((item, index) => (
                  <div key={index} className="d-flex justify-content-between mb-2">
                    <span>{item.name} × {item.quantity}</span>
                    <span>${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
                <hr />
                <div className="d-flex justify-content-between mb-2">
                  <span>Subtotal:</span>
                  <span>${orderSummary.subtotal.toFixed(2)}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Shipping:</span>
                  <span>{orderSummary.shipping === 0 ? 'FREE' : `$${orderSummary.shipping.toFixed(2)}`}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Tax:</span>
                  <span>${orderSummary.tax.toFixed(2)}</span>
                </div>
                <hr />
                <div className="d-flex justify-content-between">
                  <strong>Total:</strong>
                  <strong className="text-primary">${orderSummary.total.toFixed(2)}</strong>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default Checkout

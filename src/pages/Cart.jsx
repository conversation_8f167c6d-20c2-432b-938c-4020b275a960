import { useState } from 'react'
import { Container, <PERSON>, Col, Card, Button, Table, Form } from 'react-bootstrap'
import { Link } from 'react-router-dom'
import { FiTrash2, FiShoppingCart, FiArrowLeft } from 'react-icons/fi'

const Cart = () => {
  const [cartItems, setCartItems] = useState([
    {
      id: 1,
      name: "Classic Black NFC Card",
      price: 29.99,
      quantity: 2,
      image: "black-card"
    },
    {
      id: 2,
      name: "Premium White NFC Card", 
      price: 34.99,
      quantity: 1,
      image: "white-card"
    }
  ])

  const updateQuantity = (id, newQuantity) => {
    if (newQuantity === 0) {
      removeItem(id)
      return
    }
    setCartItems(items => 
      items.map(item => 
        item.id === id ? { ...item, quantity: newQuantity } : item
      )
    )
  }

  const removeItem = (id) => {
    setCartItems(items => items.filter(item => item.id !== id))
  }

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  const shipping = subtotal > 50 ? 0 : 9.99
  const tax = subtotal * 0.08
  const total = subtotal + shipping + tax

  if (cartItems.length === 0) {
    return (
      <Container className="py-5">
        <Row>
          <Col lg={8} className="mx-auto text-center">
            <div className="py-5">
              <FiShoppingCart size={64} className="text-muted mb-4" />
              <h2 className="fw-bold mb-3">Your Cart is Empty</h2>
              <p className="text-muted mb-4">
                Looks like you haven't added any items to your cart yet.
              </p>
              <Button as={Link} to="/products" variant="primary" className="btn-gradient">
                Continue Shopping
              </Button>
            </div>
          </Col>
        </Row>
      </Container>
    )
  }

  return (
    <div className="fade-in">
      <Container className="py-5">
        <Row>
          <Col>
            <div className="d-flex align-items-center mb-4">
              <Button as={Link} to="/products" variant="outline-secondary" className="me-3">
                <FiArrowLeft className="me-2" />
                Continue Shopping
              </Button>
              <h1 className="fw-bold mb-0">Shopping Cart ({cartItems.length} items)</h1>
            </div>
          </Col>
        </Row>

        <Row>
          <Col lg={8}>
            <Card className="border-0 shadow-sm">
              <Card.Body className="p-0">
                <Table responsive className="mb-0">
                  <thead className="bg-light">
                    <tr>
                      <th className="border-0 p-4">Product</th>
                      <th className="border-0 p-4">Price</th>
                      <th className="border-0 p-4">Quantity</th>
                      <th className="border-0 p-4">Total</th>
                      <th className="border-0 p-4"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {cartItems.map((item) => (
                      <tr key={item.id}>
                        <td className="p-4">
                          <div className="d-flex align-items-center">
                            <div 
                              className="bg-gradient-primary rounded me-3"
                              style={{ width: '60px', height: '60px' }}
                            ></div>
                            <div>
                              <h6 className="fw-bold mb-1">{item.name}</h6>
                              <small className="text-muted">SKU: NFC-{item.id}</small>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className="fw-bold">${item.price}</span>
                        </td>
                        <td className="p-4">
                          <div className="d-flex align-items-center">
                            <Button 
                              variant="outline-secondary" 
                              size="sm"
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            >
                              -
                            </Button>
                            <Form.Control 
                              type="number" 
                              value={item.quantity}
                              onChange={(e) => updateQuantity(item.id, parseInt(e.target.value) || 0)}
                              className="mx-2 text-center"
                              style={{ width: '60px' }}
                              min="0"
                            />
                            <Button 
                              variant="outline-secondary" 
                              size="sm"
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            >
                              +
                            </Button>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className="fw-bold">${(item.price * item.quantity).toFixed(2)}</span>
                        </td>
                        <td className="p-4">
                          <Button 
                            variant="outline-danger" 
                            size="sm"
                            onClick={() => removeItem(item.id)}
                          >
                            <FiTrash2 />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={4}>
            <Card className="border-0 shadow-sm">
              <Card.Header className="bg-light border-0">
                <h5 className="fw-bold mb-0">Order Summary</h5>
              </Card.Header>
              <Card.Body>
                <div className="d-flex justify-content-between mb-2">
                  <span>Subtotal:</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Shipping:</span>
                  <span>{shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Tax:</span>
                  <span>${tax.toFixed(2)}</span>
                </div>
                <hr />
                <div className="d-flex justify-content-between mb-3">
                  <strong>Total:</strong>
                  <strong className="text-primary">${total.toFixed(2)}</strong>
                </div>
                
                {shipping > 0 && (
                  <div className="alert alert-info">
                    <small>
                      Add ${(50 - subtotal).toFixed(2)} more for free shipping!
                    </small>
                  </div>
                )}

                <div className="d-grid gap-2">
                  <Button 
                    as={Link} 
                    to="/checkout" 
                    size="lg" 
                    className="btn-gradient"
                  >
                    Proceed to Checkout
                  </Button>
                  <Button 
                    as={Link} 
                    to="/products" 
                    variant="outline-primary"
                  >
                    Continue Shopping
                  </Button>
                </div>
              </Card.Body>
            </Card>

            {/* Promo Code */}
            <Card className="border-0 shadow-sm mt-3">
              <Card.Body>
                <h6 className="fw-bold mb-3">Promo Code</h6>
                <div className="d-flex gap-2">
                  <Form.Control 
                    type="text" 
                    placeholder="Enter promo code"
                  />
                  <Button variant="outline-primary">Apply</Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default Cart

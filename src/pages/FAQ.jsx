import { useState } from 'react'
import { Con<PERSON><PERSON>, <PERSON>, Col, Accordion, Form, Button } from 'react-bootstrap'
import { <PERSON>Search, FiHelpCircle } from 'react-icons/fi'

const FAQ = () => {
  const [searchTerm, setSearchTerm] = useState('')

  const faqData = [
    {
      category: "Getting Started",
      questions: [
        {
          question: "What is an NFC business card?",
          answer: "An NFC business card is a smart card embedded with Near Field Communication technology that allows you to share your contact information instantly by tapping it on any NFC-enabled smartphone. No apps are required for the recipient."
        },
        {
          question: "How do I set up my NFC card?",
          answer: "Setting up your NFC card is simple: 1) Receive your card, 2) Visit the setup link provided, 3) Enter your contact information and social media links, 4) Your card is ready to use! You can update your information anytime through our web portal."
        },
        {
          question: "Do I need an app to use NFC cards?",
          answer: "No app is required! Your NFC card works with any NFC-enabled smartphone (iPhone 7+ and most Android devices). When someone taps your card on their phone, your contact information appears instantly in their browser."
        }
      ]
    },
    {
      category: "Technical Questions",
      questions: [
        {
          question: "Which devices are compatible with NFC cards?",
          answer: "NFC cards work with iPhone 7 and newer models, and most Android devices manufactured after 2012. This covers over 95% of smartphones currently in use. For older devices, we include a QR code backup on every card."
        },
        {
          question: "What's the range of NFC technology?",
          answer: "NFC has a range of approximately 4cm (1.5 inches). This close proximity requirement actually enhances security, as it prevents accidental data sharing and ensures intentional contact exchanges."
        },
        {
          question: "Can I update my information after receiving the card?",
          answer: "Yes! One of the biggest advantages of NFC cards is that you can update your information anytime through our web portal. Changes are reflected immediately, so your card always shares current information."
        },
        {
          question: "What happens if NFC doesn't work?",
          answer: "Every card includes a QR code backup. If someone's phone doesn't support NFC or it's disabled, they can simply scan the QR code to access your information. This ensures 100% compatibility."
        }
      ]
    },
    {
      category: "Orders & Shipping",
      questions: [
        {
          question: "How long does shipping take?",
          answer: "Standard shipping takes 3-5 business days within the US. Express shipping (1-2 days) and international shipping options are available. Custom designed cards may require an additional 1-2 days for production."
        },
        {
          question: "Do you ship internationally?",
          answer: "Yes, we ship worldwide! International shipping typically takes 7-14 business days depending on the destination. Shipping costs and delivery times vary by country."
        },
        {
          question: "Can I track my order?",
          answer: "Absolutely! Once your order ships, you'll receive a tracking number via email. You can track your package's progress through our website or the carrier's tracking system."
        },
        {
          question: "What if my card arrives damaged?",
          answer: "We stand behind our quality. If your card arrives damaged or defective, contact us within 30 days for a free replacement. We'll also cover return shipping costs."
        }
      ]
    },
    {
      category: "Customization",
      questions: [
        {
          question: "Can I customize the design of my card?",
          answer: "Yes! We offer extensive customization options including colors, fonts, logos, and layouts. You can upload your own design or work with our design team to create something unique."
        },
        {
          question: "What file formats do you accept for logos?",
          answer: "We accept PNG, JPG, SVG, and PDF files. For best results, provide vector files (SVG or PDF) or high-resolution images (300 DPI minimum). Our design team can help optimize your files if needed."
        },
        {
          question: "Is there a minimum order for custom designs?",
          answer: "Custom designs are available for single cards! However, we offer volume discounts for orders of 10+ cards with the same design. Contact us for bulk pricing information."
        }
      ]
    },
    {
      category: "Privacy & Security",
      questions: [
        {
          question: "Is my information secure?",
          answer: "Yes, security is our top priority. Your data is encrypted and stored securely. You control what information is shared and can update privacy settings anytime. We never sell or share your personal information with third parties."
        },
        {
          question: "Can I control what information is shared?",
          answer: "Absolutely! You have complete control over what information appears when someone taps your card. You can include or exclude phone numbers, email addresses, social media links, and more."
        },
        {
          question: "What if I lose my card?",
          answer: "If you lose your card, you can immediately disable it through your account dashboard to prevent unauthorized use. You can then order a replacement card that will use the same profile information."
        }
      ]
    }
  ]

  const filteredFAQs = faqData.map(category => ({
    ...category,
    questions: category.questions.filter(
      q => 
        q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        q.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.questions.length > 0)

  return (
    <div className="fade-in">
      {/* Hero Section */}
      <section className="gradient-bg text-white py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <FiHelpCircle size={64} className="mb-3" />
              <h1 className="display-4 fw-bold mb-3">Frequently Asked Questions</h1>
              <p className="lead">
                Find answers to common questions about our NFC business cards. 
                Can't find what you're looking for? Contact our support team!
              </p>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Search Section */}
      <section className="py-5">
        <Container>
          <Row>
            <Col lg={6} className="mx-auto">
              <div className="position-relative">
                <FiSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
                <Form.Control
                  type="text"
                  placeholder="Search FAQs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="ps-5 py-3"
                  style={{ borderRadius: '25px' }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* FAQ Content */}
      <section className="pb-5">
        <Container>
          <Row>
            <Col lg={10} className="mx-auto">
              {filteredFAQs.length === 0 ? (
                <div className="text-center py-5">
                  <h4 className="text-muted">No results found</h4>
                  <p className="text-muted">Try adjusting your search terms or browse all categories below.</p>
                  <Button 
                    variant="outline-primary" 
                    onClick={() => setSearchTerm('')}
                  >
                    Clear Search
                  </Button>
                </div>
              ) : (
                filteredFAQs.map((category, categoryIndex) => (
                  <div key={categoryIndex} className="mb-5">
                    <h3 className="fw-bold mb-4 text-primary">{category.category}</h3>
                    <Accordion>
                      {category.questions.map((faq, questionIndex) => (
                        <Accordion.Item 
                          key={questionIndex} 
                          eventKey={`${categoryIndex}-${questionIndex}`}
                          className="mb-3 border-0 shadow-sm"
                        >
                          <Accordion.Header className="fw-bold">
                            {faq.question}
                          </Accordion.Header>
                          <Accordion.Body className="text-muted">
                            {faq.answer}
                          </Accordion.Body>
                        </Accordion.Item>
                      ))}
                    </Accordion>
                  </div>
                ))
              )}
            </Col>
          </Row>
        </Container>
      </section>

      {/* Contact CTA */}
      <section className="bg-light py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <h3 className="fw-bold mb-3">Still have questions?</h3>
              <p className="text-muted mb-4">
                Our friendly support team is here to help! Get in touch and we'll 
                respond within 24 hours.
              </p>
              <div className="d-flex gap-3 justify-content-center flex-wrap">
                <Button 
                  variant="primary" 
                  className="btn-gradient"
                  href="/contact"
                >
                  Contact Support
                </Button>
                <Button 
                  variant="outline-primary"
                  href="mailto:<EMAIL>"
                >
                  Email Us
                </Button>
                <Button 
                  variant="outline-primary"
                  href="tel:+15551234567"
                >
                  Call Us
                </Button>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Quick Links */}
      <section className="py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto">
              <h4 className="fw-bold text-center mb-4">Popular Topics</h4>
              <Row>
                <Col md={6} className="mb-3">
                  <div className="d-flex align-items-center p-3 bg-light rounded">
                    <div className="me-3">📱</div>
                    <div>
                      <h6 className="fw-bold mb-1">Device Compatibility</h6>
                      <small className="text-muted">Works with iPhone 7+ and most Android devices</small>
                    </div>
                  </div>
                </Col>
                <Col md={6} className="mb-3">
                  <div className="d-flex align-items-center p-3 bg-light rounded">
                    <div className="me-3">🎨</div>
                    <div>
                      <h6 className="fw-bold mb-1">Custom Designs</h6>
                      <small className="text-muted">Personalize your card with logos and colors</small>
                    </div>
                  </div>
                </Col>
                <Col md={6} className="mb-3">
                  <div className="d-flex align-items-center p-3 bg-light rounded">
                    <div className="me-3">🚚</div>
                    <div>
                      <h6 className="fw-bold mb-1">Fast Shipping</h6>
                      <small className="text-muted">3-5 business days standard delivery</small>
                    </div>
                  </div>
                </Col>
                <Col md={6} className="mb-3">
                  <div className="d-flex align-items-center p-3 bg-light rounded">
                    <div className="me-3">🔒</div>
                    <div>
                      <h6 className="fw-bold mb-1">Privacy & Security</h6>
                      <small className="text-muted">Your data is encrypted and secure</small>
                    </div>
                  </div>
                </Col>
              </Row>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  )
}

export default FAQ

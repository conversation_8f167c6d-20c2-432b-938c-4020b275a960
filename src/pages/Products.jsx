import { useState } from 'react'
import { Container, <PERSON>, <PERSON>, <PERSON>, Button, Badge, Form } from 'react-bootstrap'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import { FiStar, FiHeart, FiFilter, FiGrid, FiList, FiShoppingCart } from 'react-icons/fi'

const Products = () => {
  const [viewMode, setViewMode] = useState('grid')
  const [sortBy, setSortBy] = useState('featured')
  const [filterBy, setFilterBy] = useState('all')
  const [favorites, setFavorites] = useState(new Set())

  const handleAddToCart = (product) => {
    toast.success(`${product.name} added to cart!`, {
      icon: '🛒',
      style: {
        borderRadius: '10px',
        background: '#333',
        color: '#fff',
      },
    })
  }

  const handleToggleFavorite = (productId) => {
    const newFavorites = new Set(favorites)
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId)
      toast.success('Removed from favorites', { icon: '💔' })
    } else {
      newFavorites.add(productId)
      toast.success('Added to favorites!', { icon: '❤️' })
    }
    setFavorites(newFavorites)
  }

  const products = [
    {
      id: 1,
      name: "Classic Black NFC Card",
      price: 29.99,
      originalPrice: 39.99,
      rating: 4.8,
      reviews: 124,
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",
      features: ["NFC Enabled", "QR Code", "Waterproof", "Lifetime Updates"],
      badge: "Best Seller",
      category: "classic"
    },
    {
      id: 2,
      name: "Premium White NFC Card",
      price: 34.99,
      originalPrice: 44.99,
      rating: 4.9,
      reviews: 89,
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
      features: ["NFC Enabled", "QR Code", "Premium Finish", "Custom Design"],
      badge: "Premium",
      category: "premium"
    },
    {
      id: 3,
      name: "Metal NFC Card",
      price: 49.99,
      originalPrice: 59.99,
      rating: 4.7,
      reviews: 67,
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",
      features: ["Metal Body", "NFC Enabled", "Laser Engraving", "Luxury Feel"],
      badge: "Luxury",
      category: "luxury"
    },
    {
      id: 4,
      name: "Wood NFC Card",
      price: 39.99,
      originalPrice: 49.99,
      rating: 4.6,
      reviews: 45,
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop",
      features: ["Real Wood", "NFC Enabled", "Eco-Friendly", "Unique Grain"],
      badge: "Eco-Friendly",
      category: "eco"
    },
    {
      id: 5,
      name: "Transparent NFC Card",
      price: 44.99,
      originalPrice: 54.99,
      rating: 4.8,
      reviews: 78,
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop",
      features: ["Clear Design", "NFC Enabled", "Modern Look", "Durable"],
      badge: "Modern",
      category: "modern"
    },
    {
      id: 6,
      name: "Custom Design NFC Card",
      price: 59.99,
      originalPrice: 69.99,
      rating: 4.9,
      reviews: 156,
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=300&fit=crop",
      features: ["Full Customization", "NFC Enabled", "Your Branding", "Professional"],
      badge: "Customizable",
      category: "custom"
    }
  ]

  const filteredProducts = products.filter(product =>
    filterBy === 'all' || product.category === filterBy
  )

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low': return a.price - b.price
      case 'price-high': return b.price - a.price
      case 'rating': return b.rating - a.rating
      case 'name': return a.name.localeCompare(b.name)
      default: return 0
    }
  })

  const getBadgeVariant = (badge) => {
    switch (badge) {
      case "Best Seller": return "success"
      case "Premium": return "primary"
      case "Luxury": return "warning"
      case "Eco-Friendly": return "success"
      case "Modern": return "info"
      case "Customizable": return "danger"
      default: return "secondary"
    }
  }

  return (
    <div className="fade-in">
      {/* Header Section */}
      <section className="gradient-bg text-white py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <h1 className="display-4 fw-bold mb-3">Our NFC Card Collection</h1>
                <p className="lead">
                  Choose from our premium collection of smart NFC business cards.
                  Each card is designed for modern professionals who value innovation and style.
                </p>
              </motion.div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Filters and Controls */}
      <section className="py-4 bg-light">
        <Container>
          <Row className="align-items-center">
            <Col md={6}>
              <div className="d-flex gap-3 align-items-center flex-wrap">
                <Form.Select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  style={{ width: 'auto' }}
                >
                  <option value="all">All Categories</option>
                  <option value="classic">Classic</option>
                  <option value="premium">Premium</option>
                  <option value="luxury">Luxury</option>
                  <option value="eco">Eco-Friendly</option>
                  <option value="modern">Modern</option>
                  <option value="custom">Custom</option>
                </Form.Select>

                <Form.Select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  style={{ width: 'auto' }}
                >
                  <option value="featured">Featured</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                  <option value="name">Name A-Z</option>
                </Form.Select>
              </div>
            </Col>
            <Col md={6} className="text-end">
              <div className="d-flex justify-content-end align-items-center gap-2">
                <span className="text-muted small">View:</span>
                <Button
                  variant={viewMode === 'grid' ? 'primary' : 'outline-secondary'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <FiGrid />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'primary' : 'outline-secondary'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <FiList />
                </Button>
                <span className="text-muted small ms-3">
                  {sortedProducts.length} products found
                </span>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Products Grid */}
      <section className="section-padding">
        <Container>
          <Row>
            {sortedProducts.map((product, index) => (
              <Col lg={viewMode === 'grid' ? 4 : 12} md={viewMode === 'grid' ? 6 : 12} key={product.id} className="mb-4">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <Card className={`product-card h-100 border-0 shadow-sm ${viewMode === 'list' ? 'flex-row' : ''}`}>
                    <div className={`position-relative ${viewMode === 'list' ? 'flex-shrink-0' : ''}`} style={viewMode === 'list' ? { width: '300px' } : {}}>
                      <img
                        src={product.image}
                        alt={product.name}
                        className="card-img-top"
                        style={{ height: '250px', objectFit: 'cover', width: '100%' }}
                      />
                      <Badge
                        bg={getBadgeVariant(product.badge)}
                        className="position-absolute top-0 start-0 m-3"
                      >
                        {product.badge}
                      </Badge>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className={`btn position-absolute top-0 end-0 m-3 rounded-circle p-2 border-0 ${
                          favorites.has(product.id) ? 'btn-danger' : 'btn-light'
                        }`}
                        style={{ width: '40px', height: '40px' }}
                        onClick={() => handleToggleFavorite(product.id)}
                      >
                        <FiHeart fill={favorites.has(product.id) ? 'currentColor' : 'none'} />
                      </motion.button>
                    </div>

                    <Card.Body className="d-flex flex-column">
                      <h5 className="fw-bold mb-2">{product.name}</h5>

                      <div className="d-flex align-items-center mb-2">
                        <div className="d-flex align-items-center me-3">
                          {[...Array(5)].map((_, i) => (
                            <FiStar
                              key={i}
                              className={`${i < Math.floor(product.rating) ? 'text-warning' : 'text-muted'} me-1`}
                              fill={i < Math.floor(product.rating) ? 'currentColor' : 'none'}
                              size={14}
                            />
                          ))}
                          <span className="fw-bold ms-1">{product.rating}</span>
                        </div>
                        <small className="text-muted">({product.reviews} reviews)</small>
                      </div>

                      <div className="mb-3">
                        {product.features.map((feature, idx) => (
                          <Badge key={idx} bg="light" text="dark" className="me-1 mb-1 small">
                            {feature}
                          </Badge>
                        ))}
                      </div>

                      <div className="mt-auto">
                        <div className="d-flex align-items-center mb-3">
                          <span className="h5 text-primary fw-bold mb-0">${product.price}</span>
                          <span className="text-muted text-decoration-line-through ms-2">
                            ${product.originalPrice}
                          </span>
                          <Badge bg="danger" className="ms-2">
                            {Math.round((1 - product.price / product.originalPrice) * 100)}% OFF
                          </Badge>
                        </div>

                        <div className={`d-grid gap-2 ${viewMode === 'list' ? 'd-flex' : ''}`}>
                          <Button
                            as={Link}
                            to={`/product/${product.id}`}
                            variant="primary"
                            className="btn-gradient"
                          >
                            View Details
                          </Button>
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button
                              variant="outline-primary"
                              className="w-100"
                              onClick={() => handleAddToCart(product)}
                            >
                              <FiShoppingCart className="me-2" />
                              Add to Cart
                            </Button>
                          </motion.div>
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>

          {sortedProducts.length === 0 && (
            <Row>
              <Col className="text-center py-5">
                <h4 className="text-muted">No products found</h4>
                <p className="text-muted">Try adjusting your filters to see more products.</p>
                <Button variant="primary" onClick={() => setFilterBy('all')}>
                  Show All Products
                </Button>
              </Col>
            </Row>
          )}
        </Container>
      </section>

      {/* Key Benefits Section */}
      <section className="key-benefits-section">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto text-center mb-5">
              <Badge className="benefits-badge mb-3">Key Benefits</Badge>
              <h2 className="benefits-title mb-4">Product advantages</h2>
              <p className="benefits-subtitle">
                Experience the superior features that make our NFC cards the smart choice for professionals
              </p>
            </Col>
          </Row>
          <Row className="justify-content-center">
            <Col lg={4} md={6} className="mb-4">
              <Card className="benefit-card h-100 border-0">
                <Card.Body className="text-center p-4">
                  <div className="benefit-icon-simple mb-3">✅</div>
                  <h5 className="benefit-title-simple mb-3">Lifetime Updates</h5>
                  <p className="benefit-description-simple text-muted mb-0">
                    Update your information anytime without reprinting
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={4} md={6} className="mb-4">
              <Card className="benefit-card h-100 border-0">
                <Card.Body className="text-center p-4">
                  <div className="benefit-icon-simple mb-3">📱</div>
                  <h5 className="benefit-title-simple mb-3">Universal Compatibility</h5>
                  <p className="benefit-description-simple text-muted mb-0">
                    Works with all NFC-enabled smartphones
                  </p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={4} md={6} className="mb-4">
              <Card className="benefit-card h-100 border-0">
                <Card.Body className="text-center p-4">
                  <div className="benefit-icon-simple mb-3">🔒</div>
                  <h5 className="benefit-title-simple mb-3">Secure & Private</h5>
                  <p className="benefit-description-simple text-muted mb-0">
                    Your data is encrypted and protected
                  </p>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  )
}

export default Products

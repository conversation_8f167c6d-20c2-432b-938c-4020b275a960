import { useState } from 'react'
import { But<PERSON> } from 'react-bootstrap'
import { motion, AnimatePresence } from 'framer-motion'
import { FiMessageCircle, FiPhone, FiMail, FiHelpCircle, FiX } from 'react-icons/fi'

const FloatingActionButton = () => {
  const [isOpen, setIsOpen] = useState(false)

  const actions = [
    {
      icon: <FiMessageCircle />,
      label: 'Live Chat',
      color: 'success',
      action: () => console.log('Open chat')
    },
    {
      icon: <FiPhone />,
      label: 'Call Us',
      color: 'primary',
      action: () => window.open('tel:+15551234567')
    },
    {
      icon: <FiMail />,
      label: 'Email',
      color: 'info',
      action: () => window.open('mailto:<EMAIL>')
    },
    {
      icon: <FiHelpCircle />,
      label: 'Help',
      color: 'warning',
      action: () => window.open('/faq', '_blank')
    }
  ]

  return (
    <div className="position-fixed bottom-0 end-0 p-4" style={{ zIndex: 1050 }}>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            className="d-flex flex-column gap-2 mb-3"
          >
            {actions.map((action, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Button
                  variant={action.color}
                  className="rounded-circle shadow-lg d-flex align-items-center justify-content-center"
                  style={{ width: '50px', height: '50px' }}
                  onClick={action.action}
                  title={action.label}
                >
                  {action.icon}
                </Button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
      >
        <Button
          variant="primary"
          className="rounded-circle shadow-lg btn-gradient d-flex align-items-center justify-content-center"
          style={{ width: '60px', height: '60px' }}
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <FiX size={24} /> : <FiMessageCircle size={24} />}
        </Button>
      </motion.div>
    </div>
  )
}

export default FloatingActionButton

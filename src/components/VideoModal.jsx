import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap'
import { motion } from 'framer-motion'
import { FiPlay, FiX } from 'react-icons/fi'

const VideoModal = ({ show, onHide }) => {
  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      size="lg" 
      centered
      className="video-modal"
    >
      <Modal.Header className="border-0 pb-0">
        <Button 
          variant="outline-light" 
          className="ms-auto rounded-circle p-2"
          onClick={onHide}
          style={{ width: '40px', height: '40px' }}
        >
          <FiX />
        </Button>
      </Modal.Header>
      <Modal.Body className="p-0">
        <div className="ratio ratio-16x9">
          <div className="bg-dark d-flex align-items-center justify-content-center text-white">
            <div className="text-center">
              <FiPlay size={64} className="mb-3" />
              <h4>NFC Card Demo Video</h4>
              <p className="text-muted">
                See how easy it is to share your contact information with just a tap!
              </p>
              <div className="mt-4">
                <div className="bg-primary rounded-3 p-4 mx-auto" style={{ maxWidth: '300px' }}>
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="bg-white rounded-3 p-3 text-dark text-center"
                  >
                    <h6 className="mb-2">📱 Tap to Share</h6>
                    <small>Contact information shared instantly!</small>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  )
}

export default VideoModal

import { Con<PERSON><PERSON>, <PERSON>, <PERSON> } from 'react-bootstrap'
import { <PERSON> } from 'react-router-dom'
import { FiFacebook, FiTwitter, FiInstagram, FiLinkedin, FiMail, FiPhone, FiMapPin } from 'react-icons/fi'

const Footer = () => {
  return (
    <footer className="bg-dark text-light py-5 mt-5">
      <Container>
        <Row>
          <Col lg={4} md={6} className="mb-4">
            <h5 className="gradient-text fw-bold mb-3">NFCCards by EcnoDev</h5>
            <p className="text-muted">
              Revolutionize your networking with smart NFC business cards.
              Share your contact information instantly with just a tap.
              Powered by EcnoDev's innovative technology.
            </p>
            <div className="d-flex gap-3 mt-3">
              <a href="#" className="text-light">
                <FiFacebook size={20} />
              </a>
              <a href="#" className="text-light">
                <FiTwitter size={20} />
              </a>
              <a href="#" className="text-light">
                <FiInstagram size={20} />
              </a>
              <a href="#" className="text-light">
                <FiLinkedin size={20} />
              </a>
            </div>
          </Col>
          
          <Col lg={2} md={6} className="mb-4">
            <h6 className="fw-bold mb-3">Quick Links</h6>
            <ul className="list-unstyled">
              <li className="mb-2">
                <Link to="/" className="text-muted text-decoration-none">Home</Link>
              </li>
              <li className="mb-2">
                <Link to="/products" className="text-muted text-decoration-none">Products</Link>
              </li>
              <li className="mb-2">
                <Link to="/about" className="text-muted text-decoration-none">About</Link>
              </li>
              <li className="mb-2">
                <Link to="/contact" className="text-muted text-decoration-none">Contact</Link>
              </li>
            </ul>
          </Col>
          
          <Col lg={3} md={6} className="mb-4">
            <h6 className="fw-bold mb-3">Support</h6>
            <ul className="list-unstyled">
              <li className="mb-2">
                <Link to="/faq" className="text-muted text-decoration-none">FAQ</Link>
              </li>
              <li className="mb-2">
                <a href="#" className="text-muted text-decoration-none">Privacy Policy</a>
              </li>
              <li className="mb-2">
                <a href="#" className="text-muted text-decoration-none">Terms of Service</a>
              </li>
              <li className="mb-2">
                <a href="#" className="text-muted text-decoration-none">Shipping Info</a>
              </li>
            </ul>
          </Col>
          
          <Col lg={3} md={6} className="mb-4">
            <h6 className="fw-bold mb-3">Contact Info</h6>
            <div className="d-flex align-items-center mb-2">
              <FiMail className="me-2" />
              <span className="text-muted"><EMAIL></span>
            </div>
            <div className="d-flex align-items-center mb-2">
              <FiPhone className="me-2" />
              <span className="text-muted">+****************</span>
            </div>
            <div className="d-flex align-items-center">
              <FiMapPin className="me-2" />
              <span className="text-muted">123 Business St, City, State 12345</span>
            </div>
          </Col>
        </Row>
        
        <hr className="my-4" />
        
        <Row>
          <Col className="text-center">
            <p className="text-muted mb-0">
              © 2024 NFCCards by EcnoDev. All rights reserved. |
              <a href="https://ecnodev.com" className="text-decoration-none text-primary ms-1" target="_blank" rel="noopener noreferrer">
                Visit EcnoDev.com
              </a>
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  )
}

export default Footer

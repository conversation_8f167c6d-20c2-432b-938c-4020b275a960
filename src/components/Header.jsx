import { useState, useEffect } from 'react'
import { Navbar, Nav, Container, <PERSON><PERSON>, Badge, Dropdown } from 'react-bootstrap'
import { Link, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FiShoppingCart, FiMenu, FiX, FiUser, FiSearch, FiChevronDown } from 'react-icons/fi'
import Logo from './Logo'

const Header = () => {
  const [expanded, setExpanded] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const location = useLocation()

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (expanded && !event.target.closest('.navbar')) {
        setExpanded(false)
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [expanded])

  // Close mobile menu on route change
  useEffect(() => {
    setExpanded(false)
  }, [location.pathname])

  const navItems = [
    { path: '/', label: 'Digital Profile' },
    { path: '/products', label: 'Shop Now' },
    { path: '/about', label: 'For Teams' },
    {
      path: '#',
      label: 'Company',
      dropdown: true,
      items: [
        { path: '/about', label: 'About Us' },
        { path: '/faq', label: 'FAQ' },
        { path: '/contact', label: 'Support' }
      ]
    },
    { path: '/contact', label: 'Contact Us' }
  ]

  return (
    <motion.div
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Navbar
        className={`modern-navbar py-3 transition-all ${scrolled ? 'scrolled' : ''}`}
        expand="lg"
        expanded={expanded}
        fixed="top"
      >
        <Container>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Navbar.Brand as={Link} to="/" className="modern-logo d-flex align-items-center">
              <Logo size={32} className="me-2" />
              <motion.span
                className="logo-text"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                NFC<span className="logo-accent">Cards</span>
                <span className="logo-subtitle">by EcnoDev</span>
              </motion.span>
            </Navbar.Brand>
          </motion.div>

          <Navbar.Toggle
            aria-controls="basic-navbar-nav"
            onClick={() => setExpanded(!expanded)}
            className="border-0 p-2"
            aria-label="Toggle navigation"
          >
            <motion.div
              animate={{ rotate: expanded ? 180 : 0 }}
              transition={{ duration: 0.3 }}
              className="d-flex align-items-center justify-content-center"
            >
              {expanded ? (
                <FiX size={20} className="text-white" />
              ) : (
                <FiMenu size={20} className="text-white" />
              )}
            </motion.div>
          </Navbar.Toggle>

          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="mx-auto">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  {item.dropdown ? (
                    <Dropdown>
                      <Dropdown.Toggle
                        as="a"
                        className="nav-link modern-nav-link d-flex align-items-center justify-content-center"
                        style={{ cursor: 'pointer', textDecoration: 'none' }}
                      >
                        {item.label}
                        <motion.div
                          animate={{ rotate: 0 }}
                          whileHover={{ rotate: 180 }}
                          transition={{ duration: 0.3 }}
                        >
                          <FiChevronDown className="ms-2" size={14} />
                        </motion.div>
                      </Dropdown.Toggle>
                      <Dropdown.Menu className="modern-dropdown shadow-lg">
                        {item.items.map((subItem) => (
                          <Dropdown.Item
                            key={subItem.path}
                            as={Link}
                            to={subItem.path}
                            onClick={() => setExpanded(false)}
                            className="d-flex align-items-center"
                          >
                            {subItem.label}
                          </Dropdown.Item>
                        ))}
                      </Dropdown.Menu>
                    </Dropdown>
                  ) : (
                    <Nav.Link
                      as={Link}
                      to={item.path}
                      onClick={() => setExpanded(false)}
                      className={`modern-nav-link ${location.pathname === item.path ? 'active' : ''}`}
                    >
                      {item.label}
                      {location.pathname === item.path && (
                        <motion.div
                          className="nav-indicator"
                          layoutId="activeTab"
                        />
                      )}
                    </Nav.Link>
                  )}
                </motion.div>
              ))}
            </Nav>

            <div className="nav-actions d-flex align-items-center justify-content-center flex-wrap">
              <motion.button
                className="action-btn search-btn"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Search"
              >
                <FiSearch size={18} />
              </motion.button>

              <motion.button
                className="action-btn profile-btn"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Profile"
              >
                <FiUser size={18} />
              </motion.button>

              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button
                  as={Link}
                  to="/cart"
                  className="action-btn cart-btn position-relative"
                  onClick={() => setExpanded(false)}
                  aria-label="Shopping cart"
                >
                  <FiShoppingCart size={18} />
                  <Badge
                    className="cart-badge position-absolute top-0 start-100 translate-middle"
                    bg="success"
                    pill
                  >
                    2
                  </Badge>
                </Button>
              </motion.div>
            </div>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      {/* Spacer for fixed navbar */}
      <div style={{ height: '80px' }}></div>
    </motion.div>
  )
}

export default Header
